import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { TokenTrackingData } from '@/lib/ai/tokenTracker';

export async function POST(req: NextRequest) {
  try {
    console.log('🔄 API /tokens/save iniciado');
    
    // Crear cliente de Supabase del servidor
    const supabase = await createServerSupabaseClient();
    
    // Obtener el usuario autenticado
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);
      return NextResponse.json({ error: 'Usuario no autenticado' }, { status: 401 });
    }
    
    console.log('👤 Usuario autenticado:', user.id, user.email);
    
    // Obtener los datos del cuerpo de la petición
    const data: TokenTrackingData = await req.json();
    console.log('📝 Datos recibidos:', data);
    
    // Preparar el registro para insertar
    const usageRecord = {
      user_id: user.id,
      activity_type: data.activity,
      model_name: data.model,
      prompt_tokens: data.usage.promptTokens,
      completion_tokens: data.usage.completionTokens,
      total_tokens: data.usage.totalTokens,
      estimated_cost: data.usage.estimatedCost || 0,
      usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01
    };
    
    console.log('📝 Registro a insertar:', usageRecord);
    
    // Insertar en la base de datos
    const { error } = await supabase
      .from('user_token_usage')
      .insert([usageRecord]);
    
    if (error) {
      console.error('❌ Error al guardar uso de tokens:', error);
      return NextResponse.json({ error: 'Error al guardar tokens' }, { status: 500 });
    }
    
    console.log('✅ Registro insertado exitosamente en user_token_usage');
    
    // Actualizar contador mensual del usuario
    await updateMonthlyTokenCount(supabase, user.id, data.usage.totalTokens);
    
    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('❌ Error en API /tokens/save:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

/**
 * Actualiza el contador mensual de tokens del usuario
 */
async function updateMonthlyTokenCount(supabase: any, userId: string, tokens: number): Promise<void> {
  try {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    // Obtener o crear perfil del usuario
    let { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error al obtener perfil:', profileError);
      return;
    }

    if (!profile) {
      // Crear perfil nuevo
      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert([{
          user_id: userId,
          subscription_plan: 'free',
          monthly_token_limit: 50000,
          current_month_tokens: tokens,
          current_month: currentMonth
        }]);

      if (insertError) {
        console.error('Error al crear perfil:', insertError);
      } else {
        console.log('✅ Perfil de usuario creado');
      }
    } else {
      // Actualizar perfil existente
      const newTokenCount = profile.current_month === currentMonth 
        ? profile.current_month_tokens + tokens 
        : tokens; // Reset si es nuevo mes

      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          current_month_tokens: newTokenCount,
          current_month: currentMonth,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error al actualizar perfil:', updateError);
      } else {
        console.log('✅ Perfil de usuario actualizado');
      }
    }
  } catch (error) {
    console.error('Error en updateMonthlyTokenCount:', error);
  }
}
