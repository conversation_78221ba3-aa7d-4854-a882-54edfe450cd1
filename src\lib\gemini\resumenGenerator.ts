import { prepararDocumentos } from './geminiClient';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';

// Prompt para resúmenes (copiado desde prompts.ts para evitar conflictos de tipos)
const PROMPT_RESUMENES = `
Eres "Mentor Opositor AI", un preparador de élite especializado en la redacción de temas para el segundo ejercicio de las oposiciones a los Cuerpos Superiores (A1 y A2) de la Administración Pública Española. Tu misión es generar un desarrollo temático completo y exhaustivo, redactado al nivel de excelencia exigido por los tribunales de oposición.

Tu resultado no debe ser un resumen, sino el **texto modelo completo que un opositor de alto rendimiento memorizaría y desarrollaría en su examen**.

**TÍTULO DEL TEMA A DESARROLLAR:**
{titulo_del_tema}

**INFORMACIÓN BASE (Documentación, legislación, apuntes):**
{documento}

**PETICIÓN ADICIONAL DEL OPOSITOR (si la hubiera):**
{instrucciones}

---

**INSTRUCCIONES DE REDACCIÓN OBLIGATORIAS:**

**1. ESTRUCTURA FORMAL DE TEMA DE OPOSICIÓN (ESQUEMA NUMERADO):**
   El tema debe seguir una estructura jerárquica y numerada, esencial para la claridad y la memorización.

   - **Introducción:**
     - No debe ser un mero resumen. Debe **encuadrar el tema** en su contexto (constitucional, legal, administrativo).
     - Debe **justificar su importancia** (p. ej., "Este tema es capital para entender el funcionamiento del Estado de Derecho...", "La correcta gestión de este procedimiento es garantía de los derechos de los ciudadanos...").
     - Debe **anunciar la estructura** del desarrollo (p. ej., "A lo largo de este tema, analizaremos en primer lugar..., para después abordar..., y finalmente concluir con...").

   - **Desarrollo del Tema (Cuerpo Principal):**
     - Organiza TODO el contenido en **epígrafes y sub-epígrafes numerados** (ej: 1., 1.1., 1.2., 2., 2.1., etc.). Esta es la parte más importante.
     - La estructura debe ser lógica, progresiva y deductiva. De lo general a lo particular.
     - Cada epígrafe debe desarrollar una idea principal de forma completa y rigurosa.

   - **Conclusión:**
     - Debe ser una **síntesis valorativa**, no una simple repetición.
     - Recapitula las ideas clave de forma breve.
     - Ofrece una reflexión final sobre la relevancia actual del tema, su evolución reciente o posibles retos futuros (p. ej., el impacto de la digitalización, la jurisprudencia más reciente del TS o TC, etc.).

**2. REQUISITOS DE CONTENIDO Y ESTILO (NIVEL A1/A2):**

   - **Rigor Jurídico-Administrativo:**
     - **Cita explícitamente la normativa clave**: Constitución Española (CE), Leyes (Ley 39/2015, Ley 40/2015, TREBEP, etc.), Reglamentos, Directivas Europeas. Usa la fórmula completa (p. ej., "según el artículo 103.1 de la Constitución Española", "la Ley 39/2015, de 1 de octubre, del Procedimiento Administrativo Común de las Administraciones Públicas").
     - Si es pertinente, menciona **sentencias clave** del Tribunal Constitucional o del Tribunal Supremo.
     - Utiliza la **terminología técnica precisa** y define los conceptos fundamentales con claridad académica.

   - **Profundidad y Conexión:**
     - No te limites a describir. **Analiza, relaciona y contextualiza**.
     - Conecta el tema con otros conceptos relevantes del temario (p. ej., si el tema es sobre el acto administrativo, conéctalo con los principios del procedimiento, los recursos, etc.).
     - Incluye referencias a la **evolución histórica** de la institución o concepto, si es relevante para entender su estado actual.

   - **Lenguaje:**
     - Utiliza un **lenguaje formal, objetivo y académico**, propio de un jurista o alto funcionario. Evita coloquialismos y opiniones personales no fundamentadas.
     - La redacción debe ser fluida, clara y precisa.

**3. FORMATO Y EXTENSIÓN:**

   - **Formato:** Utiliza **Markdown** para una perfecta jerarquía visual.
     - # Título del Tema
     - ## INTRODUCCIÓN
     - ## 1. PRIMER EPÍGRAFE PRINCIPAL
     - ### 1.1. Primer Sub-epígrafe
     - ## 2. SEGUNDO EPÍGRAFE PRINCIPAL
     - ## CONCLUSIÓN
     - Usa **negrita** para destacar conceptos o artículos imprescindibles.
     - Usa \`>\` para citar textualmente un artículo de una ley o una definición doctrinal importante.

   - **Extensión:**
     - **IMPORTANTÍSIMO**: El desarrollo completo del tema debe tener una extensión aproximada de **2800 a 3500 palabras**. Este es el volumen necesario para un desarrollo exhaustivo que pueda aspirar a la máxima nota.

**MANDATO FINAL:**
Tu objetivo no es facilitar el estudio con un resumen. Tu objetivo es **crear el material de estudio definitivo**: un tema redactado con la calidad, estructura y profundidad que un miembro del Tribunal calificador esperaría leer. El opositor debe poder coger tu texto, estudiarlo, memorizar su estructura y sus puntos clave, y replicarlo en el examen para obtener una calificación sobresaliente. Procede.
`;

/**
 * Genera un resumen de un documento usando IA
 */
export async function generarResumen(
  documento: { titulo: string; contenido: string; categoria?: string; numero_tema?: number },
  instrucciones?: string
): Promise<string> {
  try {
    // Validar entrada
    if (!documento || !documento.contenido) {
      throw new Error("No se ha proporcionado un documento válido para generar el resumen.");
    }

    // Validar que el contenido no esté vacío
    if (documento.contenido.trim().length === 0) {
      throw new Error("El contenido del documento está vacío.");
    }

    // Preparar el contenido del documento usando el mismo patrón que otros servicios
    const documentos = [documento];
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se pudo preparar el contenido del documento.");
    }

    // Validar y limpiar instrucciones
    const instruccionesLimpias = instrucciones?.trim() || 'Crea un resumen completo y estructurado del tema proporcionado.';

    // Construir el prompt final siguiendo el patrón de otros servicios
    let finalPrompt = PROMPT_RESUMENES.replace('{documento}', contenidoDocumentos);
    finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

    // Obtener configuración específica para resúmenes
    const config = getOpenAIConfig('RESUMENES');

    console.log(`📄 Generando resumen con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

    // Generar el resumen usando OpenAI con la configuración correcta
    const messages = [{ role: 'user' as const, content: finalPrompt }];
    const responseText = await llamarOpenAI(messages, {
      ...config,
      activityName: 'Generación de Resumen'
    });

    // Validar que la respuesta no esté vacía
    if (!responseText || responseText.trim().length === 0) {
      throw new Error("La IA no generó ningún contenido para el resumen.");
    }

    // Limpiar y formatear la respuesta
    let resumenContent = responseText.trim();

    // Asegurar que el resumen tenga un formato mínimo
    if (resumenContent.length < 100) {
      throw new Error("El resumen generado es demasiado corto. Por favor, inténtalo de nuevo.");
    }

    console.log('✅ Resumen generado exitosamente');
    return resumenContent;

  } catch (error) {
    console.error('Error al generar resumen:', error);

    // Proporcionar un mensaje de error más descriptivo si es posible
    if (error instanceof Error) {
      throw new Error(`Error al generar el resumen: ${error.message}`);
    }

    throw new Error("Ha ocurrido un error inesperado al generar el resumen.");
  }
}
