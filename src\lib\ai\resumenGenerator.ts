import { llamarOpenAI } from './openaiService';
import { getOpenAIConfig } from '@/config/openai';
import { PROMPT_RESUMENES } from '@/config/prompts';

/**
 * Genera un resumen de un documento usando IA
 */
export async function generarResumen(
  documento: { titulo: string; contenido: string; categoria?: string; numero_tema?: number },
  instrucciones?: string
): Promise<string> {
  try {
    // Validar entrada
    if (!documento || !documento.contenido) {
      throw new Error("No se ha proporcionado un documento válido para generar el resumen.");
    }

    // Validar que el contenido no esté vacío
    if (documento.contenido.trim().length === 0) {
      throw new Error("El contenido del documento está vacío.");
    }

    // Preparar el contenido del documento
    const contenidoDocumento = `
**Título:** ${documento.titulo}
${documento.numero_tema ? `**Tema:** ${documento.numero_tema}` : ''}
${documento.categoria ? `**Categoría:** ${documento.categoria}` : ''}

**Contenido:**
${documento.contenido}
    `.trim();

    // Validar y limpiar instrucciones
    const instruccionesLimpias = instrucciones?.trim() || 'Crea un resumen completo y estructurado del tema proporcionado.';

    // Construir el prompt final
    let finalPrompt = PROMPT_RESUMENES.replace('{documento}', contenidoDocumento);
    finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

    // Obtener configuración específica para resúmenes
    const config = getOpenAIConfig('RESUMENES');

    console.log(`📄 Generando resumen con modelo: ${config.model} (max_tokens: ${config.max_tokens})`);

    // Generar el resumen usando OpenAI
    const messages = [{ role: 'user' as const, content: finalPrompt }];
    const responseText = await llamarOpenAI(messages, {
      ...config,
      activityName: 'Generación de Resumen'
    });

    // Validar que la respuesta no esté vacía
    if (!responseText || responseText.trim().length === 0) {
      throw new Error("La IA no generó ningún contenido para el resumen.");
    }

    // Limpiar y formatear la respuesta
    let resumenContent = responseText.trim();

    // Asegurar que el resumen tenga un formato mínimo
    if (resumenContent.length < 100) {
      throw new Error("El resumen generado es demasiado corto. Por favor, inténtalo de nuevo.");
    }

    console.log('✅ Resumen generado exitosamente');
    return resumenContent;

  } catch (error) {
    console.error('Error al generar resumen:', error);

    // Proporcionar un mensaje de error más descriptivo
    if (error instanceof Error) {
      throw new Error(`Error al generar el resumen: ${error.message}`);
    }

    throw new Error("Ha ocurrido un error inesperado al generar el resumen.");
  }
}

/**
 * Valida si un documento es apto para generar resumen
 */
export function validarDocumentoParaResumen(documento: any): { valido: boolean; error?: string } {
  if (!documento) {
    return { valido: false, error: 'No se ha proporcionado ningún documento' };
  }

  if (!documento.titulo || documento.titulo.trim().length === 0) {
    return { valido: false, error: 'El documento debe tener un título' };
  }

  if (!documento.contenido || documento.contenido.trim().length === 0) {
    return { valido: false, error: 'El documento debe tener contenido' };
  }

  if (documento.contenido.trim().length < 50) {
    return { valido: false, error: 'El contenido del documento es demasiado corto para generar un resumen útil' };
  }

  return { valido: true };
}

/**
 * Estima el tiempo de generación basado en el tamaño del documento
 */
export function estimarTiempoGeneracion(documento: any): number {
  if (!documento || !documento.contenido) {
    return 30; // 30 segundos por defecto
  }

  const palabras = documento.contenido.split(/\s+/).length;
  
  // Estimación: ~1 segundo por cada 100 palabras, mínimo 15 segundos, máximo 120 segundos
  const tiempoEstimado = Math.max(15, Math.min(120, Math.ceil(palabras / 100)));
  
  return tiempoEstimado;
}
