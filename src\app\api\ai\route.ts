import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { generarTest, generarFlashcards, generarMapaMental } from '@/lib/gemini';
import { obtenerRespuestaIA } from '@/lib/gemini/questionService';
import { generarPlanEstudios } from '@/features/planificacion/services/planGeneratorService';
import { generarResumen } from '@/lib/ai/resumenGenerator';
import { ApiAIInputSchema } from '@/lib/zodSchemas';
import { logTokenUsage, createOpenAITokenTracking } from '@/lib/ai/tokenTracker';
import { getOpenAIConfig } from '@/config/openai';

// API route for AI actions (formerly Gemini)
export async function POST(req: NextRequest) {
  try {
    // Crear cliente de Supabase usando la implementación correcta
    const supabase = await createServerSupabaseClient();

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({
        error: 'Unauthorized',
        debug: {
          userError: userError?.message,
          hasCookies: !!req.headers.get('cookie')
        }
      }, { status: 401 });
    }

    const body = await req.json();

    // Validación robusta de entrada
    const parseResult = ApiAIInputSchema.safeParse(body);
    if (!parseResult.success) {
      return NextResponse.json({
        error: 'Datos inválidos',
        detalles: parseResult.error.errors
      }, { status: 400 });
    }

    // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA
    if (body.pregunta && body.documentos) {
      const result = await obtenerRespuestaIA(body.pregunta, body.documentos);

      // ✅ CORREGIDO: No hacer tracking aquí porque ya se hace en llamarOpenAI()
      // El tracking se realiza automáticamente en openaiClient.ts con datos reales

      return NextResponse.json({ result });
    }

    const { action, peticion, contextos, cantidad, temarioId } = body;

    let result;
    let activityName = '';
    let promptText = '';

    switch (action) {
      case 'generarTest':
        activityName = `Generación de Test (${cantidad || 'N/A'} preguntas)`;
        promptText = `${peticion} - Contextos: ${contextos?.join(', ') || 'N/A'}`;
        result = await generarTest(peticion, contextos, cantidad);
        break;
      case 'generarFlashcards':
        activityName = `Generación de Flashcards (${cantidad || 'N/A'} tarjetas)`;
        promptText = `${peticion} - Contextos: ${contextos?.join(', ') || 'N/A'}`;
        result = await generarFlashcards(peticion, contextos, cantidad);
        break;
      case 'generarMapaMental':
        activityName = 'Generación de Mapa Mental';
        promptText = `${peticion} - Contextos: ${contextos?.join(', ') || 'N/A'}`;
        result = await generarMapaMental(peticion, contextos);
        break;
      case 'generarResumen':
        activityName = 'Generación de Resumen';
        promptText = `${peticion} - Documento: ${contextos?.[0]?.substring(0, 100) || 'N/A'}...`;

        // Para resúmenes, esperamos que contextos[0] sea el contenido del documento
        if (!contextos || contextos.length !== 1) {
          throw new Error('Se requiere exactamente un documento para generar un resumen');
        }

        // Crear objeto documento a partir del contexto
        const documento = {
          titulo: peticion.split('|')[0] || 'Documento sin título',
          contenido: contextos[0],
          categoria: peticion.split('|')[1],
          numero_tema: peticion.split('|')[2] ? parseInt(peticion.split('|')[2]) : undefined
        };

        const instrucciones = peticion.split('|')[3] || undefined;
        result = await generarResumen(documento, instrucciones);
        break;
      case 'generarPlanEstudios':
        // Para planes de estudios, el temarioId viene en peticion
        const temarioIdFromPeticion = peticion || temarioId;
        if (!temarioIdFromPeticion) {
          throw new Error('Se requiere temarioId para generar el plan de estudios');
        }
        activityName = 'Generación de Plan de Estudios';
        promptText = `Temario ID: ${temarioIdFromPeticion}`;
        result = await generarPlanEstudios(temarioIdFromPeticion, user);
        break;
      default:
        return NextResponse.json({ error: 'Acción no soportada' }, { status: 400 });
    }

    // ✅ CORREGIDO: No hacer tracking aquí porque ya se hace en llamarOpenAI()
    // Cada función (generarTest, generarFlashcards, etc.) usa llamarOpenAI() internamente
    // que ya registra automáticamente el uso real de tokens con el modelo correcto

    return NextResponse.json({ result });

  } catch (error: any) {
    console.error('❌ Error en API AI:', error);
    
    return NextResponse.json({
      error: 'Error interno del servidor',
      detalles: error.message
    }, { status: 500 });
  }
}
