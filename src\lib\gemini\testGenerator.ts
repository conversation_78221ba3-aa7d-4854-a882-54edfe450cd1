import { model, prepararDocumentos } from './geminiClient';
import { PROMPT_TESTS } from '../../config/prompts';

/**
 * Genera un test con preguntas de opción múltiple a partir de los documentos
 */
export async function generarTest(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}[]> {
  try {
    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se han proporcionado documentos para generar el test.");
    }

    // Construir el prompt para la IA usando el prompt personalizado
    // Reemplazar las variables en el prompt
    let prompt = PROMPT_TESTS
      .replace('{documentos}', contenidoDocumentos)
      .replace('{cantidad}', cantidad.toString());

    // Añadir instrucciones adicionales si se proporcionan
    if (instrucciones) {
      prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
    } else {
      prompt = prompt.replace('{instrucciones}', '');
    }

    // Generar el test
    const result = await model.generateContent(prompt);
    const response = result.response;
    const responseText = response.text();

    // Extraer el JSON de la respuesta
    const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

    if (!jsonMatch) {
      console.log('❌ No se encontró JSON en la respuesta. Respuesta recibida:', responseText.substring(0, 500));
      throw new Error("No se pudo extraer el formato JSON de la respuesta.");
    }

    const testJson = jsonMatch[0];
    const preguntas = JSON.parse(testJson);

    // Validar el formato
    if (!Array.isArray(preguntas) || preguntas.length === 0) {
      throw new Error("El formato de las preguntas generadas no es válido.");
    }

    // Validar que cada pregunta tiene el formato correcto
    preguntas.forEach((pregunta, index) => {
      if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b ||
          !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {
        throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);
      }

      // Asegurarse de que la respuesta correcta es una de las opciones válidas
      if (!['a', 'b', 'c', 'd'].includes(pregunta.respuesta_correcta)) {
        throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);
      }
    });

    return preguntas;
  } catch (error) {
    console.error('Error al generar test:', error);
    throw error;
  }
}
