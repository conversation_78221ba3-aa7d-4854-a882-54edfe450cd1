-- Agregar columna tema_id a la tabla tests para asociar tests con temas específicos
-- Esta columna permitirá hacer repasos por temas

-- Verificar si la columna ya existe antes de agregarla
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'tests' AND column_name = 'tema_id') THEN
        ALTER TABLE tests ADD COLUMN tema_id UUID REFERENCES temas(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Crear índice para mejorar el rendimiento de las consultas por tema (solo si no existe)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tests_tema_id') THEN
        CREATE INDEX idx_tests_tema_id ON tests(tema_id);
    END IF;
END $$;

-- Comentario sobre la columna
COMMENT ON COLUMN tests.tema_id IS 'ID del tema asociado al test para permitir repasos por tema específico';

-- Verificar que todo se creó correctamente
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'tests' AND column_name = 'tema_id';
