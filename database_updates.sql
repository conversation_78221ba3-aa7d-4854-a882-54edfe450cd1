-- Agregar columna tema_id a la tabla tests para asociar tests con temas específicos
-- Esta columna permitirá hacer repasos por temas

ALTER TABLE tests ADD COLUMN tema_id UUID REFERENCES temas(id) ON DELETE SET NULL;

-- <PERSON><PERSON>r índice para mejorar el rendimiento de las consultas por tema
CREATE INDEX idx_tests_tema_id ON tests(tema_id);

-- Comentario sobre la columna
COMMENT ON COLUMN tests.tema_id IS 'ID del tema asociado al test para permitir repasos por tema específico';
