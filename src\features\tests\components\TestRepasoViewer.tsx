import React, { useState, useEffect } from 'react';
import { PreguntaTest } from '@/lib/supabase/supabaseClient';
import { FiChevronLeft, FiChevronRight, FiCheck, FiX, FiClock, FiTarget } from 'react-icons/fi';

interface ConfiguracionTest {
  testId: string;
  cantidad: number;
  maxPreguntas: number;
}

interface TestRepasoViewerProps {
  preguntas: PreguntaTest[];
  configuracion: ConfiguracionTest[];
  onFinalizar: (resultados: ResultadoRepaso) => void;
  onCancelar: () => void;
}

interface RespuestaUsuario {
  preguntaId: string;
  respuestaSeleccionada: 'a' | 'b' | 'c' | 'd' | null;
  esCorrecta: boolean;
  tiempoRespuesta: number;
}

interface ResultadoRepaso {
  totalPreguntas: number;
  respuestasCorrectas: number;
  respuestasIncorrectas: number;
  tiempoTotal: number;
  porcentajeAcierto: number;
  respuestas: RespuestaUsuario[];
}

export default function TestRepasoViewer({ preguntas, configuracion, onFinalizar, onCancelar }: TestRepasoViewerProps) {
  const [preguntaActual, setPreguntaActual] = useState(0);
  const [respuestas, setRespuestas] = useState<RespuestaUsuario[]>([]);
  const [respuestaSeleccionada, setRespuestaSeleccionada] = useState<'a' | 'b' | 'c' | 'd' | null>(null);
  const [mostrarRespuesta, setMostrarRespuesta] = useState(false);
  const [tiempoInicio, setTiempoInicio] = useState<number>(Date.now());
  const [tiempoPregunta, setTiempoPregunta] = useState<number>(Date.now());
  const [testFinalizado, setTestFinalizado] = useState(false);

  useEffect(() => {
    setTiempoInicio(Date.now());
    setTiempoPregunta(Date.now());
  }, []);

  useEffect(() => {
    // Resetear cuando cambia la pregunta
    setRespuestaSeleccionada(null);
    setMostrarRespuesta(false);
    setTiempoPregunta(Date.now());
  }, [preguntaActual]);

  const pregunta = preguntas[preguntaActual];

  const handleRespuesta = (opcion: 'a' | 'b' | 'c' | 'd') => {
    if (mostrarRespuesta) return;
    
    setRespuestaSeleccionada(opcion);
    setMostrarRespuesta(true);
    
    const tiempoRespuesta = Date.now() - tiempoPregunta;
    const esCorrecta = opcion === pregunta.respuesta_correcta;
    
    const nuevaRespuesta: RespuestaUsuario = {
      preguntaId: pregunta.id,
      respuestaSeleccionada: opcion,
      esCorrecta,
      tiempoRespuesta
    };
    
    setRespuestas(prev => [...prev, nuevaRespuesta]);
  };

  const siguientePregunta = () => {
    if (preguntaActual < preguntas.length - 1) {
      setPreguntaActual(prev => prev + 1);
    } else {
      finalizarTest();
    }
  };

  const preguntaAnterior = () => {
    if (preguntaActual > 0) {
      setPreguntaActual(prev => prev - 1);
      // Buscar si ya hay respuesta para esta pregunta
      const respuestaExistente = respuestas.find(r => r.preguntaId === preguntas[preguntaActual - 1].id);
      if (respuestaExistente) {
        setRespuestaSeleccionada(respuestaExistente.respuestaSeleccionada);
        setMostrarRespuesta(true);
      }
    }
  };

  const finalizarTest = () => {
    const tiempoTotal = Date.now() - tiempoInicio;
    const respuestasCorrectas = respuestas.filter(r => r.esCorrecta).length;
    const respuestasIncorrectas = respuestas.length - respuestasCorrectas;
    const porcentajeAcierto = (respuestasCorrectas / preguntas.length) * 100;

    const resultados: ResultadoRepaso = {
      totalPreguntas: preguntas.length,
      respuestasCorrectas,
      respuestasIncorrectas,
      tiempoTotal,
      porcentajeAcierto,
      respuestas
    };

    setTestFinalizado(true);
    onFinalizar(resultados);
  };

  const getOpcionClass = (opcion: 'a' | 'b' | 'c' | 'd') => {
    if (!mostrarRespuesta) {
      return 'hover:bg-gray-50 cursor-pointer';
    }
    
    if (opcion === pregunta.respuesta_correcta) {
      return 'bg-green-100 border-green-500 text-green-800';
    }
    
    if (opcion === respuestaSeleccionada && opcion !== pregunta.respuesta_correcta) {
      return 'bg-red-100 border-red-500 text-red-800';
    }
    
    return 'bg-gray-50';
  };

  const getOpcionIcon = (opcion: 'a' | 'b' | 'c' | 'd') => {
    if (!mostrarRespuesta) return null;
    
    if (opcion === pregunta.respuesta_correcta) {
      return <FiCheck className="text-green-600" />;
    }
    
    if (opcion === respuestaSeleccionada && opcion !== pregunta.respuesta_correcta) {
      return <FiX className="text-red-600" />;
    }
    
    return null;
  };

  const progreso = ((preguntaActual + 1) / preguntas.length) * 100;
  const tiempoTranscurrido = Math.floor((Date.now() - tiempoInicio) / 1000);

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header con progreso */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Test de Repaso</h2>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <FiTarget className="h-4 w-4" />
              {preguntaActual + 1} de {preguntas.length}
            </div>
            <div className="flex items-center gap-1">
              <FiClock className="h-4 w-4" />
              {Math.floor(tiempoTranscurrido / 60)}:{(tiempoTranscurrido % 60).toString().padStart(2, '0')}
            </div>
          </div>
        </div>
        
        {/* Barra de progreso */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progreso}%` }}
          ></div>
        </div>
      </div>

      {/* Pregunta actual */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-medium mb-6">{pregunta.pregunta}</h3>
        
        <div className="space-y-3">
          {(['a', 'b', 'c', 'd'] as const).map((opcion) => (
            <div
              key={opcion}
              className={`p-4 border rounded-lg transition-all ${getOpcionClass(opcion)}`}
              onClick={() => handleRespuesta(opcion)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium">
                    {opcion.toUpperCase()}
                  </div>
                  <span>{pregunta[`opcion_${opcion}` as keyof PreguntaTest] as string}</span>
                </div>
                {getOpcionIcon(opcion)}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Controles de navegación */}
      <div className="flex justify-between items-center">
        <button
          onClick={preguntaAnterior}
          disabled={preguntaActual === 0}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
            preguntaActual === 0
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <FiChevronLeft className="h-4 w-4" />
          Anterior
        </button>

        <div className="flex gap-2">
          <button
            onClick={onCancelar}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancelar
          </button>
          
          {mostrarRespuesta && (
            <button
              onClick={siguientePregunta}
              className="flex items-center gap-2 px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
            >
              {preguntaActual === preguntas.length - 1 ? 'Finalizar' : 'Siguiente'}
              {preguntaActual < preguntas.length - 1 && <FiChevronRight className="h-4 w-4" />}
            </button>
          )}
        </div>
      </div>

      {/* Indicador de respuesta */}
      {!mostrarRespuesta && (
        <div className="text-center text-gray-500 text-sm">
          Selecciona una respuesta para continuar
        </div>
      )}
    </div>
  );
}
