// src/lib/ai/tokenTracker.ts

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  estimatedCost?: number;
}

export interface TokenTrackingData {
  activity: string;
  model: string;
  usage: TokenUsage;
  timestamp: Date;
  userId?: string;
}

// Precios por 1M tokens (en USD) - Actualizados según OpenAI
const TOKEN_PRICES = {
  'gpt-4o': {
    input: 2.50,  // $2.50 per 1M tokens
    output: 10.00 // $10.00 per 1M tokens
  },
  'gpt-4o-mini': {
    input: 0.15,  // $0.15 per 1M tokens
    output: 0.60  // $0.60 per 1M tokens
  },
  'gpt-4.1-mini': {
    input: 0.40,  // $0.40 per 1M tokens
    output: 1.60  // $1.60 per 1M tokens
  },
  'o1-mini': {
    input: 3.00,  // $3.00 per 1M tokens
    output: 12.00 // $12.00 per 1M tokens
  },
  'o3-mini': {
    input: 1.10,  // $1.10 per 1M tokens
    output: 4.40  // $4.40 per 1M tokens
  },
  'o4-mini': {
    input: 1.10,  // $1.10 per 1M tokens
    output: 4.40  // $4.40 per 1M tokens
  }
} as const;

/**
 * Calcula el costo estimado basado en el uso de tokens
 */
export function calculateEstimatedCost(model: string, usage: TokenUsage): number {
  // Normalizar el nombre del modelo eliminando sufijos de fecha
  let normalizedModel = model.toLowerCase();

  // Remover sufijos de fecha como "-2025-04-16"
  normalizedModel = normalizedModel.replace(/-\d{4}-\d{2}-\d{2}$/, '');

  const prices = TOKEN_PRICES[normalizedModel as keyof typeof TOKEN_PRICES];

  if (!prices) {
    console.warn(`⚠️ Precios no encontrados para el modelo: ${model} (normalizado: ${normalizedModel})`);
    return 0;
  }

  // Convertir de tokens a millones de tokens para el cálculo (precios son por 1M tokens)
  const inputCost = (usage.promptTokens / 1000000) * prices.input;
  const outputCost = (usage.completionTokens / 1000000) * prices.output;

  return inputCost + outputCost;
}

/**
 * Estima tokens para texto (aproximación para Gemini)
 */
export function estimateTokens(text: string): number {
  // Aproximación: 1 token ≈ 4 caracteres para texto en español
  return Math.ceil(text.length / 4);
}

/**
 * Formatea el output del tracking para la consola
 */
export function formatTokenTracking(data: TokenTrackingData): string {
  const { activity, model, usage, timestamp } = data;

  const timeStr = timestamp.toLocaleTimeString('es-ES');

  // Solo mostrar tokens al usuario, el precio se calcula internamente para límites
  return `🤖 [${timeStr}] ${activity} | ${model} | ` +
         `📥 ${usage.promptTokens} → 📤 ${usage.completionTokens} = ` +
         `🔢 ${usage.totalTokens} tokens`;
}

/**
 * Registra el uso de tokens en la consola y Supabase
 */
export function logTokenUsage(data: TokenTrackingData): void {
  const formattedLog = formatTokenTracking(data);
  console.log(formattedLog);

  // Guardar en Supabase (asíncrono, no bloquea)
  if (typeof window !== 'undefined') {
    console.log('🔄 Intentando guardar tokens en Supabase...', data);
    import('@/lib/supabase/tokenUsageService').then(({ saveTokenUsage }) => {
      console.log('✅ Servicio de Supabase importado correctamente');
      saveTokenUsage(data)
        .then(() => {
          console.log('✅ Tokens guardados exitosamente en Supabase');
        })
        .catch(error => {
          console.error('❌ Error al guardar en Supabase:', error);
        });
    }).catch(importError => {
      console.error('❌ Error al importar servicio de Supabase:', importError);
    });
  }

  // También guardar en localStorage como backup
  if (typeof window !== 'undefined') {
    try {
      const existingLogs = JSON.parse(localStorage.getItem('ai-token-logs') || '[]');
      existingLogs.push(data);

      // Mantener solo los últimos 100 registros
      if (existingLogs.length > 100) {
        existingLogs.splice(0, existingLogs.length - 100);
      }

      localStorage.setItem('ai-token-logs', JSON.stringify(existingLogs));
    } catch (error) {
      console.warn('No se pudo guardar el log de tokens en localStorage:', error);
    }
  }
}

/**
 * Crea un objeto de tracking de tokens para OpenAI
 */
export function createOpenAITokenTracking(
  activity: string,
  model: string,
  usage: any,
  userId?: string
): TokenTrackingData {
  const tokenUsage: TokenUsage = {
    promptTokens: usage.prompt_tokens || 0,
    completionTokens: usage.completion_tokens || 0,
    totalTokens: usage.total_tokens || 0
  };

  tokenUsage.estimatedCost = calculateEstimatedCost(model, tokenUsage);

  return {
    activity,
    model,
    usage: tokenUsage,
    timestamp: new Date(),
    userId
  };
}



/**
 * Obtiene estadísticas de uso de tokens desde localStorage
 */
export function getTokenUsageStats(): {
  totalSessions: number;
  totalTokens: number;
  totalCost: number;
  byActivity: Record<string, { tokens: number; cost: number; count: number }>;
  byModel: Record<string, { tokens: number; cost: number; count: number }>;
} {
  if (typeof window === 'undefined') {
    return {
      totalSessions: 0,
      totalTokens: 0,
      totalCost: 0,
      byActivity: {},
      byModel: {}
    };
  }

  try {
    const logs: TokenTrackingData[] = JSON.parse(localStorage.getItem('ai-token-logs') || '[]');
    
    const stats = {
      totalSessions: logs.length,
      totalTokens: 0,
      totalCost: 0,
      byActivity: {} as Record<string, { tokens: number; cost: number; count: number }>,
      byModel: {} as Record<string, { tokens: number; cost: number; count: number }>
    };

    logs.forEach(log => {
      const tokens = log.usage.totalTokens;
      const cost = log.usage.estimatedCost || 0;

      stats.totalTokens += tokens;
      stats.totalCost += cost;

      // Por actividad
      if (!stats.byActivity[log.activity]) {
        stats.byActivity[log.activity] = { tokens: 0, cost: 0, count: 0 };
      }
      stats.byActivity[log.activity].tokens += tokens;
      stats.byActivity[log.activity].cost += cost;
      stats.byActivity[log.activity].count += 1;

      // Por modelo
      if (!stats.byModel[log.model]) {
        stats.byModel[log.model] = { tokens: 0, cost: 0, count: 0 };
      }
      stats.byModel[log.model].tokens += tokens;
      stats.byModel[log.model].cost += cost;
      stats.byModel[log.model].count += 1;
    });

    return stats;
  } catch (error) {
    console.warn('Error al obtener estadísticas de tokens:', error);
    return {
      totalSessions: 0,
      totalTokens: 0,
      totalCost: 0,
      byActivity: {},
      byModel: {}
    };
  }
}
