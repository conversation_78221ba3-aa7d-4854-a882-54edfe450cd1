import React, { useState, useEffect } from 'react';
import { Tema, Test } from '@/lib/supabase/supabaseClient';
import { obtenerTemasDisponibles, obtenerTestsPorTemas } from '../services/testsService';
import { FiBook, FiPlay, FiCheckCircle } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

interface RepasoTemasProps {
  onIniciarRepaso: (tests: Test[], temasSeleccionados: Tema[]) => void;
}

export default function RepasoTemas({ onIniciarRepaso }: RepasoTemasProps) {
  const [temasDisponibles, setTemasDisponibles] = useState<Tema[]>([]);
  const [temasSeleccionados, setTemasSeleccionados] = useState<string[]>([]);
  const [testsEncontrados, setTestsEncontrados] = useState<Test[]>([]);
  const [cargando, setCargando] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    cargarTemas();
  }, []);

  useEffect(() => {
    if (temasSeleccionados.length > 0) {
      buscarTestsPorTemas();
    } else {
      setTestsEncontrados([]);
    }
  }, [temasSeleccionados]);

  const cargarTemas = async () => {
    setCargando(true);
    try {
      const temas = await obtenerTemasDisponibles();
      setTemasDisponibles(temas);
    } catch (error) {
      console.error('Error al cargar temas:', error);
      toast.error('No se pudieron cargar los temas disponibles.');
    } finally {
      setCargando(false);
    }
  };

  const buscarTestsPorTemas = async () => {
    setCargando(true);
    try {
      const tests = await obtenerTestsPorTemas(temasSeleccionados);
      setTestsEncontrados(tests);
    } catch (error) {
      console.error('Error al buscar tests por temas:', error);
      toast.error('No se pudieron cargar los tests de los temas seleccionados.');
    } finally {
      setCargando(false);
    }
  };

  const handleTemaToggle = (temaId: string) => {
    setTemasSeleccionados(prev => 
      prev.includes(temaId) 
        ? prev.filter(id => id !== temaId)
        : [...prev, temaId]
    );
  };

  const handleSeleccionarTodos = () => {
    if (temasSeleccionados.length === temasDisponibles.length) {
      setTemasSeleccionados([]);
    } else {
      setTemasSeleccionados(temasDisponibles.map(tema => tema.id));
    }
  };

  const handleIniciarRepaso = () => {
    if (testsEncontrados.length === 0) {
      setError('No hay tests disponibles para los temas seleccionados.');
      return;
    }

    const temasSeleccionadosObj = temasDisponibles.filter(tema => 
      temasSeleccionados.includes(tema.id)
    );

    onIniciarRepaso(testsEncontrados, temasSeleccionadosObj);
  };

  if (cargando && temasDisponibles.length === 0) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <span className="ml-2 text-gray-600">Cargando temas...</span>
      </div>
    );
  }

  if (temasDisponibles.length === 0) {
    return (
      <div className="text-center py-8">
        <FiBook className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No hay temas disponibles</h3>
        <p className="mt-1 text-sm text-gray-500">
          Primero necesitas crear un temario con temas para poder hacer repasos específicos.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-4">Repaso por Temas</h2>
        <p className="text-gray-600 mb-4">
          Selecciona los temas de los que quieres hacer un repaso. Se incluirán todos los tests asociados a esos temas.
        </p>
      </div>

      {/* Controles de selección */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Seleccionar Temas</h3>
        <button
          onClick={handleSeleccionarTodos}
          className="text-sm text-indigo-600 hover:text-indigo-800"
        >
          {temasSeleccionados.length === temasDisponibles.length ? 'Deseleccionar todos' : 'Seleccionar todos'}
        </button>
      </div>

      {/* Lista de temas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {temasDisponibles.map(tema => (
          <div
            key={tema.id}
            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
              temasSeleccionados.includes(tema.id)
                ? 'border-indigo-500 bg-indigo-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleTemaToggle(tema.id)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">
                  Tema {tema.numero}: {tema.titulo}
                </h4>
                {tema.descripcion && (
                  <p className="text-sm text-gray-500 mt-1">{tema.descripcion}</p>
                )}
              </div>
              <div className="ml-2">
                {temasSeleccionados.includes(tema.id) ? (
                  <FiCheckCircle className="h-5 w-5 text-indigo-600" />
                ) : (
                  <div className="h-5 w-5 border border-gray-300 rounded-full"></div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Información de tests encontrados */}
      {temasSeleccionados.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">Tests encontrados</h4>
          {cargando ? (
            <p className="text-sm text-gray-600">Buscando tests...</p>
          ) : (
            <div>
              <p className="text-sm text-gray-600 mb-2">
                Se encontraron {testsEncontrados.length} test(s) para los temas seleccionados.
              </p>
              {testsEncontrados.length > 0 && (
                <div className="space-y-1">
                  {testsEncontrados.slice(0, 5).map(test => (
                    <p key={test.id} className="text-xs text-gray-500">
                      • {test.titulo}
                    </p>
                  ))}
                  {testsEncontrados.length > 5 && (
                    <p className="text-xs text-gray-500">
                      ... y {testsEncontrados.length - 5} más
                    </p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {error && (
        <div className="text-red-500 text-sm bg-red-50 p-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Botón de iniciar repaso */}
      <div className="flex justify-center">
        <button
          onClick={handleIniciarRepaso}
          disabled={temasSeleccionados.length === 0 || testsEncontrados.length === 0 || cargando}
          className={`flex items-center px-6 py-3 rounded-lg font-medium ${
            temasSeleccionados.length === 0 || testsEncontrados.length === 0 || cargando
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-indigo-600 text-white hover:bg-indigo-700'
          }`}
        >
          <FiPlay className="mr-2 h-5 w-5" />
          Iniciar Repaso ({testsEncontrados.length} tests)
        </button>
      </div>
    </div>
  );
}
