// Configuración centralizada de OpenAI

export const OPENAI_CONFIG = {
  // Modelo por defecto para todas las operaciones
  DEFAULT_MODEL: 'gpt-4o-mini',
  
  // Modelos específicos por funcionalidad
  MODELS: {
    // Para generación de planes de estudio (requiere más precisión)
    PLAN_ESTUDIOS: 'gpt-4o', // Modelo más potente para planes complejos

    // Para Q&A y conversaciones (puede ser más rápido)
    CONVERSACIONES: 'gpt-4o-mini',

    // Para generación de flashcards (simple)
    FLASHCARDS: 'gpt-4o-mini',

    // Para generación de tests (requiere precisión)
    TESTS: 'gpt-4o-mini',

    // Para mapas mentales (creativo)
    MAPAS_MENTALES: 'gpt-4o'
  },
  
  // Configuraciones por defecto
  DEFAULTS: {
    temperature: 0.7,
    max_tokens: 4000,
  },
  
  // Configuraciones específicas por tipo de tarea
  TASK_CONFIGS: {
    PLAN_ESTUDIOS: {
      temperature: 0.3, // Más determinístico para planes
      max_tokens: 16000, // Límite alto para planes largos y complejos
    },
    CONVERSACIONES: {
      temperature: 0.7, // Más natural para conversaciones
      max_tokens: 2000,
    },
    FLASHCARDS: {
      temperature: 0.6, // Equilibrado para flashcards
      max_tokens: 1500,
    },
    TESTS: {
      temperature: 0.4, // Muy determinístico para tests
      max_tokens: 3000,
    },
    MAPAS_MENTALES: {
      temperature: 0.8, // Más creativo para mapas
      max_tokens: 2500,
    },
  }
};

// Función helper para obtener configuración por tipo de tarea
export function getOpenAIConfig(taskType: keyof typeof OPENAI_CONFIG.MODELS) {
  return {
    model: OPENAI_CONFIG.MODELS[taskType],
    ...OPENAI_CONFIG.TASK_CONFIGS[taskType],
  };
}

// Función para obtener el modelo por defecto
export function getDefaultModel() {
  return OPENAI_CONFIG.DEFAULT_MODEL;
}
