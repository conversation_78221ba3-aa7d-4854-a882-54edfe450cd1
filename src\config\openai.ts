// Configuración centralizada de OpenAI

export const OPENAI_CONFIG = {
  // Modelo por defecto para todas las operaciones
  DEFAULT_MODEL: 'gpt-4o-mini',
  
  // Modelos específicos por funcionalidad
  MODELS: {
    // Para generación de planes de estudio (requiere más precisión)
    PLAN_ESTUDIOS: 'gpt-4o', // Modelo estable y confiable

    // Para Q&A y conversaciones (puede ser más rápido)
    CONVERSACIONES: 'gpt-4o-mini',

    // Para generación de flashcards (simple)
    FLASHCARDS: 'gpt-4o-mini',

    // Para generación de tests (requiere precisión)
    TESTS: 'gpt-4o-mini',

    // Para mapas mentales (creativo) - Modelos disponibles para testing
    MAPAS_MENTALES: 'o3-mini' // Modelo de razonamiento con 100k tokens output
  },

  // Modelos alternativos para mapas mentales (para testing)
  MAPAS_MENTALES_ALTERNATIVES: {
    'o3-mini': 'o3-mini',      // Razonamiento, 100k tokens, $4.40/1M
    'o4-mini': 'o4-mini',      // Razonamiento, 100k tokens, $4.40/1M
    'gpt-4.1': 'gpt-4.1',      // Tradicional, 32k tokens, $8.00/1M
    'gpt-4o': 'gpt-4o',        // Tradicional, estable
    'gpt-4o-mini': 'gpt-4o-mini' // Tradicional, económico
  },
  
  // Configuraciones por defecto
  DEFAULTS: {
    temperature: 0.7,
    max_tokens: 4000,
  },
  
  // Configuraciones específicas por tipo de tarea
  TASK_CONFIGS: {
    PLAN_ESTUDIOS: {
      temperature: 0.3, // Más determinístico para planes
      max_tokens: 16000, // Límite alto para planes largos y complejos
    },
    CONVERSACIONES: {
      temperature: 0.7, // Más natural para conversaciones
      max_tokens: 2000,
    },
    FLASHCARDS: {
      temperature: 0.6, // Equilibrado para flashcards
      max_tokens: 1500,
    },
    TESTS: {
      temperature: 0.4, // Muy determinístico para tests
      max_tokens: 3000,
    },
    MAPAS_MENTALES: {
      temperature: 0.8, // Más creativo para mapas (solo para modelos tradicionales)
      max_tokens: 15000, // Aumentado significativamente para mapas complejos
    },
  }
};

// Función helper para obtener configuración por tipo de tarea
export function getOpenAIConfig(taskType: keyof typeof OPENAI_CONFIG.MODELS) {
  return {
    model: OPENAI_CONFIG.MODELS[taskType],
    ...OPENAI_CONFIG.TASK_CONFIGS[taskType],
  };
}

// Función para obtener el modelo por defecto
export function getDefaultModel() {
  return OPENAI_CONFIG.DEFAULT_MODEL;
}

// Función para obtener configuración de mapas mentales con modelo específico
export function getMapasMentalesConfig(modelo?: string) {
  const modeloSeleccionado = modelo || OPENAI_CONFIG.MODELS.MAPAS_MENTALES;

  // Verificar si el modelo está en las alternativas disponibles
  const modelosDisponibles = Object.values(OPENAI_CONFIG.MAPAS_MENTALES_ALTERNATIVES);
  const modeloFinal = modelosDisponibles.includes(modeloSeleccionado)
    ? modeloSeleccionado
    : OPENAI_CONFIG.MODELS.MAPAS_MENTALES;

  return {
    model: modeloFinal,
    ...OPENAI_CONFIG.TASK_CONFIGS.MAPAS_MENTALES,
  };
}

// Función para listar modelos disponibles para mapas mentales
export function getModelosMapasMentalesDisponibles() {
  return OPENAI_CONFIG.MAPAS_MENTALES_ALTERNATIVES;
}
