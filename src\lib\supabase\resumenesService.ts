import { supabase, Resumen } from './supabaseClient';
import { obtenerUsuarioActual } from './authService';

/**
 * Obtiene todos los resúmenes del usuario actual
 */
export async function obtenerResumenes(): Promise<Resumen[]> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return [];
    }

    const { data, error } = await supabase
      .from('resumenes')
      .select('*')
      .eq('user_id', user.id)
      .order('creado_en', { ascending: false });

    if (error) {
      console.error('Error al obtener resúmenes:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener resúmenes:', error);
    return [];
  }
}

/**
 * Obtiene un resumen específico por ID
 */
export async function obtenerResumenPorId(id: string): Promise<Resumen | null> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('resumenes')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener resumen:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener resumen:', error);
    return null;
  }
}

/**
 * Verifica si ya existe un resumen para un documento específico
 */
export async function existeResumenParaDocumento(documentoId: string): Promise<boolean> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      return false;
    }

    const { data, error } = await supabase
      .from('resumenes')
      .select('id')
      .eq('user_id', user.id)
      .eq('documento_id', documentoId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error al verificar resumen existente:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('Error al verificar resumen existente:', error);
    return false;
  }
}

/**
 * Guarda un nuevo resumen en la base de datos
 */
export async function guardarResumen(
  documentoId: string,
  titulo: string,
  contenido: string,
  instrucciones?: string
): Promise<string | null> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    // Verificar si ya existe un resumen para este documento
    const existe = await existeResumenParaDocumento(documentoId);
    if (existe) {
      console.error('Ya existe un resumen para este documento');
      return null;
    }

    const { data, error } = await supabase
      .from('resumenes')
      .insert([{
        user_id: user.id,
        documento_id: documentoId,
        titulo,
        contenido,
        instrucciones
      }])
      .select()
      .single();

    if (error) {
      console.error('Error al guardar resumen:', error);
      return null;
    }

    return data?.id || null;
  } catch (error) {
    console.error('Error al guardar resumen:', error);
    return null;
  }
}

/**
 * Actualiza un resumen existente
 */
export async function actualizarResumen(
  id: string,
  titulo: string,
  contenido: string,
  instrucciones?: string
): Promise<boolean> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return false;
    }

    const { error } = await supabase
      .from('resumenes')
      .update({
        titulo,
        contenido,
        instrucciones,
        actualizado_en: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error al actualizar resumen:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al actualizar resumen:', error);
    return false;
  }
}

/**
 * Elimina un resumen
 */
export async function eliminarResumen(id: string): Promise<boolean> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return false;
    }

    const { error } = await supabase
      .from('resumenes')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id);

    if (error) {
      console.error('Error al eliminar resumen:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error al eliminar resumen:', error);
    return false;
  }
}
