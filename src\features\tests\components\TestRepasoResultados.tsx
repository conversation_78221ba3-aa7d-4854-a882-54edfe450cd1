import React from 'react';
import { FiCheck, FiX, FiClock, FiTarget, FiRefreshCw, FiHome } from 'react-icons/fi';

interface RespuestaUsuario {
  preguntaId: string;
  respuestaSeleccionada: 'a' | 'b' | 'c' | 'd' | null;
  esCorrecta: boolean;
  tiempoRespuesta: number;
}

interface ResultadoRepaso {
  totalPreguntas: number;
  respuestasCorrectas: number;
  respuestasIncorrectas: number;
  tiempoTotal: number;
  porcentajeAcierto: number;
  respuestas: RespuestaUsuario[];
}

interface ConfiguracionTest {
  testId: string;
  cantidad: number;
  maxPreguntas: number;
}

interface TestRepasoResultadosProps {
  resultados: ResultadoRepaso;
  configuracion: ConfiguracionTest[];
  onNuevoRepaso: () => void;
  onVolver: () => void;
}

export default function TestRepasoResultados({ 
  resultados, 
  configuracion, 
  onNuevoRepaso, 
  onVolver 
}: TestRepasoResultadosProps) {
  const formatearTiempo = (milisegundos: number) => {
    const segundos = Math.floor(milisegundos / 1000);
    const minutos = Math.floor(segundos / 60);
    const segs = segundos % 60;
    return `${minutos}:${segs.toString().padStart(2, '0')}`;
  };

  const getColorPorcentaje = (porcentaje: number) => {
    if (porcentaje >= 80) return 'text-green-600';
    if (porcentaje >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getIconoPorcentaje = (porcentaje: number) => {
    if (porcentaje >= 80) return <FiCheck className="h-8 w-8 text-green-600" />;
    if (porcentaje >= 60) return <FiTarget className="h-8 w-8 text-yellow-600" />;
    return <FiX className="h-8 w-8 text-red-600" />;
  };

  const getMensajePorcentaje = (porcentaje: number) => {
    if (porcentaje >= 90) return '¡Excelente trabajo!';
    if (porcentaje >= 80) return '¡Muy bien!';
    if (porcentaje >= 70) return 'Buen trabajo';
    if (porcentaje >= 60) return 'Puedes mejorar';
    return 'Necesitas más práctica';
  };

  const tiempoPromedioPorPregunta = resultados.tiempoTotal / resultados.totalPreguntas;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Resultado principal */}
      <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
        <div className="flex justify-center mb-4">
          {getIconoPorcentaje(resultados.porcentajeAcierto)}
        </div>
        
        <h2 className="text-2xl font-bold mb-2">Test de Repaso Completado</h2>
        <p className="text-gray-600 mb-6">{getMensajePorcentaje(resultados.porcentajeAcierto)}</p>
        
        <div className={`text-4xl font-bold mb-2 ${getColorPorcentaje(resultados.porcentajeAcierto)}`}>
          {resultados.porcentajeAcierto.toFixed(1)}%
        </div>
        
        <p className="text-gray-600">
          {resultados.respuestasCorrectas} de {resultados.totalPreguntas} preguntas correctas
        </p>
      </div>

      {/* Estadísticas detalladas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-2">
            <FiCheck className="h-5 w-5 text-green-600" />
            <h3 className="font-medium">Respuestas Correctas</h3>
          </div>
          <div className="text-2xl font-bold text-green-600">
            {resultados.respuestasCorrectas}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-2">
            <FiX className="h-5 w-5 text-red-600" />
            <h3 className="font-medium">Respuestas Incorrectas</h3>
          </div>
          <div className="text-2xl font-bold text-red-600">
            {resultados.respuestasIncorrectas}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-2">
            <FiClock className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium">Tiempo Total</h3>
          </div>
          <div className="text-2xl font-bold text-blue-600">
            {formatearTiempo(resultados.tiempoTotal)}
          </div>
        </div>
      </div>

      {/* Información adicional */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-medium mb-4">Detalles del Repaso</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Tests utilizados:</span>
            <span className="ml-2 font-medium">{configuracion.filter(c => c.cantidad > 0).length}</span>
          </div>
          <div>
            <span className="text-gray-600">Tiempo promedio por pregunta:</span>
            <span className="ml-2 font-medium">{formatearTiempo(tiempoPromedioPorPregunta)}</span>
          </div>
          <div>
            <span className="text-gray-600">Total de preguntas:</span>
            <span className="ml-2 font-medium">{resultados.totalPreguntas}</span>
          </div>
          <div>
            <span className="text-gray-600">Porcentaje de acierto:</span>
            <span className={`ml-2 font-medium ${getColorPorcentaje(resultados.porcentajeAcierto)}`}>
              {resultados.porcentajeAcierto.toFixed(1)}%
            </span>
          </div>
        </div>
      </div>

      {/* Distribución de preguntas por test */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-medium mb-4">Distribución de Preguntas</h3>
        <div className="space-y-2">
          {configuracion
            .filter(config => config.cantidad > 0)
            .map((config, index) => (
              <div key={config.testId} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm text-gray-600">Test {index + 1}</span>
                <span className="font-medium">{config.cantidad} pregunta{config.cantidad !== 1 ? 's' : ''}</span>
              </div>
            ))
          }
        </div>
      </div>

      {/* Análisis de rendimiento */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-medium mb-4">Análisis de Rendimiento</h3>
        <div className="space-y-3">
          {resultados.porcentajeAcierto >= 80 && (
            <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
              <FiCheck className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-green-800">¡Excelente dominio!</p>
                <p className="text-sm text-green-700">
                  Tienes un muy buen conocimiento de los temas repasados.
                </p>
              </div>
            </div>
          )}
          
          {resultados.porcentajeAcierto >= 60 && resultados.porcentajeAcierto < 80 && (
            <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
              <FiTarget className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="font-medium text-yellow-800">Buen progreso</p>
                <p className="text-sm text-yellow-700">
                  Vas por buen camino. Considera repasar los temas donde tuviste más errores.
                </p>
              </div>
            </div>
          )}
          
          {resultados.porcentajeAcierto < 60 && (
            <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
              <FiX className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <p className="font-medium text-red-800">Necesitas más práctica</p>
                <p className="text-sm text-red-700">
                  Te recomendamos repasar más estos temas antes de continuar.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Botones de acción */}
      <div className="flex justify-center gap-4">
        <button
          onClick={onNuevoRepaso}
          className="flex items-center gap-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
        >
          <FiRefreshCw className="h-5 w-5" />
          Nuevo Repaso
        </button>
        
        <button
          onClick={onVolver}
          className="flex items-center gap-2 px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
        >
          <FiHome className="h-5 w-5" />
          Volver a Tests
        </button>
      </div>
    </div>
  );
}
