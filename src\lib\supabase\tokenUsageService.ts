import { createClient } from './client';
import { TokenTrackingData } from '@/lib/ai/tokenTracker';

export interface TokenUsageRecord {
  id: string;
  user_id: string;
  activity_type: string;
  model_name: string;
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  estimated_cost: number;
  usage_month: string;
  created_at: string;
}

export interface TokenUsageStats {
  totalSessions: number;
  totalTokens: number;
  totalCost: number;
  byActivity: Record<string, { tokens: number; cost: number; count: number }>;
  byModel: Record<string, { tokens: number; cost: number; count: number }>;
}

export interface UserProfile {
  id: string;
  user_id: string;
  subscription_plan: 'free' | 'usuario' | 'pro';
  monthly_token_limit: number;
  current_month_tokens: number;
  current_month: string;
  created_at: string;
  updated_at: string;
}

/**
 * Guarda el uso de tokens en Supabase
 */
export async function saveTokenUsage(data: TokenTrackingData): Promise<void> {
  try {
    console.log('🔄 saveTokenUsage iniciado con data:', data);

    // Detectar si estamos en servidor o cliente
    const isServer = typeof window === 'undefined';
    console.log(`🌍 Ejecutándose en: ${isServer ? 'Servidor' : 'Cliente'}`);

    if (isServer) {
      // En el servidor, usar el servicio específico del servidor
      return await saveTokenUsageServer(data);
    }

    // En el cliente, usar el cliente normal
    const supabase = createClient();
    console.log('✅ Cliente Supabase creado');

    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');

    if (userError || !user) {
      console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);
      return;
    }

    const usageRecord = {
      user_id: user.id,
      activity_type: data.activity,
      model_name: data.model,
      prompt_tokens: data.usage.promptTokens,
      completion_tokens: data.usage.completionTokens,
      total_tokens: data.usage.totalTokens,
      estimated_cost: data.usage.estimatedCost || 0,
      usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01
    };

    console.log('📝 Registro a insertar:', usageRecord);

    const { error } = await supabase
      .from('user_token_usage')
      .insert([usageRecord]);

    if (error) {
      console.error('❌ Error al guardar uso de tokens:', error);
      return;
    }

    console.log('✅ Registro insertado exitosamente en user_token_usage');

    // Actualizar contador mensual del usuario
    await updateMonthlyTokenCount(user.id, data.usage.totalTokens);

  } catch (error) {
    console.error('Error en saveTokenUsage:', error);
  }
}

/**
 * Actualiza el contador mensual de tokens del usuario
 */
async function updateMonthlyTokenCount(userId: string, tokens: number): Promise<void> {
  try {
    const supabase = createClient();
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';

    // Obtener o crear perfil del usuario
    let { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error al obtener perfil:', profileError);
      return;
    }

    if (!profile) {
      // Crear perfil nuevo
      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert([{
          user_id: userId,
          subscription_plan: 'free',
          monthly_token_limit: 50000,
          current_month_tokens: tokens,
          current_month: currentMonth
        }]);

      if (insertError) {
        console.error('Error al crear perfil:', insertError);
      }
    } else {
      // Actualizar perfil existente
      const newTokenCount = profile.current_month === currentMonth 
        ? profile.current_month_tokens + tokens 
        : tokens; // Reset si es nuevo mes

      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          current_month_tokens: newTokenCount,
          current_month: currentMonth,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error al actualizar perfil:', updateError);
      }
    }
  } catch (error) {
    console.error('Error en updateMonthlyTokenCount:', error);
  }
}

/**
 * Obtiene estadísticas de uso de tokens del usuario actual
 */
export async function getUserTokenStats(): Promise<TokenUsageStats> {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return getEmptyStats();
    }

    const { data: records, error } = await supabase
      .from('user_token_usage')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error al obtener estadísticas:', error);
      return getEmptyStats();
    }

    return calculateStats(records || []);

  } catch (error) {
    console.error('Error en getUserTokenStats:', error);
    return getEmptyStats();
  }
}

/**
 * Calcula estadísticas a partir de los registros
 */
function calculateStats(records: TokenUsageRecord[]): TokenUsageStats {
  const stats: TokenUsageStats = {
    totalSessions: records.length,
    totalTokens: 0,
    totalCost: 0,
    byActivity: {},
    byModel: {}
  };

  records.forEach(record => {
    const tokens = record.total_tokens;
    const cost = record.estimated_cost;

    stats.totalTokens += tokens;
    stats.totalCost += cost;

    // Por actividad
    if (!stats.byActivity[record.activity_type]) {
      stats.byActivity[record.activity_type] = { tokens: 0, cost: 0, count: 0 };
    }
    stats.byActivity[record.activity_type].tokens += tokens;
    stats.byActivity[record.activity_type].cost += cost;
    stats.byActivity[record.activity_type].count += 1;

    // Por modelo
    if (!stats.byModel[record.model_name]) {
      stats.byModel[record.model_name] = { tokens: 0, cost: 0, count: 0 };
    }
    stats.byModel[record.model_name].tokens += tokens;
    stats.byModel[record.model_name].cost += cost;
    stats.byModel[record.model_name].count += 1;
  });

  return stats;
}

/**
 * Retorna estadísticas vacías
 */
function getEmptyStats(): TokenUsageStats {
  return {
    totalSessions: 0,
    totalTokens: 0,
    totalCost: 0,
    byActivity: {},
    byModel: {}
  };
}

/**
 * Obtiene el perfil del usuario actual
 */
export async function getUserProfile(): Promise<UserProfile | null> {
  try {
    const supabase = createClient();
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return null;
    }

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error al obtener perfil:', error);
      return null;
    }

    return profile;

  } catch (error) {
    console.error('Error en getUserProfile:', error);
    return null;
  }
}

/**
 * Verifica si el usuario ha alcanzado su límite mensual
 */
export async function checkTokenLimit(): Promise<{ 
  hasReachedLimit: boolean; 
  currentTokens: number; 
  limit: number; 
  percentage: number 
}> {
  try {
    const profile = await getUserProfile();
    
    if (!profile) {
      return { hasReachedLimit: false, currentTokens: 0, limit: 50000, percentage: 0 };
    }

    const percentage = (profile.current_month_tokens / profile.monthly_token_limit) * 100;
    const hasReachedLimit = profile.current_month_tokens >= profile.monthly_token_limit;

    return {
      hasReachedLimit,
      currentTokens: profile.current_month_tokens,
      limit: profile.monthly_token_limit,
      percentage
    };

  } catch (error) {
    console.error('Error en checkTokenLimit:', error);
    return { hasReachedLimit: false, currentTokens: 0, limit: 50000, percentage: 0 };
  }
}

/**
 * Función específica para guardar tokens en el servidor
 */
async function saveTokenUsageServer(data: TokenTrackingData): Promise<void> {
  try {
    console.log('🔄 saveTokenUsageServer iniciado');

    // Importar dinámicamente para evitar problemas de SSR
    const { createServerSupabaseClient } = await import('./server');
    const supabase = await createServerSupabaseClient();
    console.log('✅ Cliente Supabase del servidor creado');

    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('👤 Usuario obtenido:', user ? `ID: ${user.id}, Email: ${user.email}` : 'No autenticado');

    if (userError || !user) {
      console.warn('❌ No hay usuario autenticado para guardar tokens:', userError?.message);
      return;
    }

    const usageRecord = {
      user_id: user.id,
      activity_type: data.activity,
      model_name: data.model,
      prompt_tokens: data.usage.promptTokens,
      completion_tokens: data.usage.completionTokens,
      total_tokens: data.usage.totalTokens,
      estimated_cost: data.usage.estimatedCost || 0,
      usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01
    };

    console.log('📝 Registro a insertar (servidor):', usageRecord);

    const { error } = await supabase
      .from('user_token_usage')
      .insert([usageRecord]);

    if (error) {
      console.error('❌ Error al guardar uso de tokens (servidor):', error);
      return;
    }

    console.log('✅ Registro insertado exitosamente en user_token_usage (servidor)');

  } catch (error) {
    console.error('❌ Error en saveTokenUsageServer:', error);
  }
}
